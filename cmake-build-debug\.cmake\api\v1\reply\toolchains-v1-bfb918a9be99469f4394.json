{"kind": "toolchains", "toolchains": [{"compiler": {"id": "Clang", "implicit": {"includeDirectories": [], "linkDirectories": [], "linkFrameworkDirectories": [], "linkLibraries": []}, "path": "C:/Program Files/LLVM/bin/clang.exe", "version": "20.1.1"}, "language": "C", "sourceFileExtensions": ["c", "m"]}, {"compiler": {"id": "Clang", "implicit": {"includeDirectories": [], "linkDirectories": [], "linkFrameworkDirectories": [], "linkLibraries": []}, "path": "C:/Program Files/LLVM/bin/clang++.exe", "version": "20.1.1"}, "language": "CXX", "sourceFileExtensions": ["C", "M", "c++", "cc", "cpp", "cxx", "mm", "mpp", "CPP", "ixx", "cppm", "ccm", "cxxm", "c++m"]}, {"compiler": {"implicit": {}, "path": "C:/Program Files/LLVM/bin/llvm-rc.exe"}, "language": "RC", "sourceFileExtensions": ["rc", "RC"]}], "version": {"major": 1, "minor": 0}}