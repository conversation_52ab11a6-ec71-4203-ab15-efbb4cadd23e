on:
  workflow_call:

jobs:
  bvt-nvhpc:
    runs-on: ubuntu-24.04
    steps:
    - uses: actions/checkout@v4

    - name: install NVHPC 25.7
      run: |
        curl https://developer.download.nvidia.com/hpc-sdk/ubuntu/DEB-GPG-KEY-NVIDIA-HPC-SDK | sudo gpg --dearmor -o /usr/share/keyrings/nvidia-hpcsdk-archive-keyring.gpg
        echo 'deb [signed-by=/usr/share/keyrings/nvidia-hpcsdk-archive-keyring.gpg] https://developer.download.nvidia.com/hpc-sdk/ubuntu/amd64 /' | sudo tee /etc/apt/sources.list.d/nvhpc.list
        sudo apt-get update -y
        sudo apt-get install -y nvhpc-25-7

    - name: build and run test with NVHPC 25.7
      run: |
        PATH=/opt/nvidia/hpc_sdk/Linux_x86_64/25.7/compilers/bin:$PATH; export PATH
        cmake -B build -GNinja -DCMAKE_C_COMPILER=nvc -DCMAKE_CXX_COMPILER=nvc++ -DCMAKE_BUILD_TYPE=Release -DPROXY_BUILD_MODULES=FALSE
        cmake --build build -j
        ctest --test-dir build -j
        mkdir build/drop
        chmod +x tools/dump_build_env.sh
        ./tools/dump_build_env.sh nvc++ build/drop/env-info.json

    - name: run benchmarks
      run: |
        build/benchmarks/msft_proxy_benchmarks --benchmark_min_warmup_time=0.1 --benchmark_min_time=0.1s --benchmark_repetitions=30 --benchmark_enable_random_interleaving=true --benchmark_report_aggregates_only=true --benchmark_format=json > build/drop/benchmarking-results.json

    - name: archive benchmarking results
      uses: actions/upload-artifact@v4
      with:
        name: drop-nvhpc
        path: build/drop/
