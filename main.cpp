#include <iostream>
#include <format>
// Use header instead of module for better VSCode IntelliSense support
#include <proxy/v4/proxy.h>
// Uncomment the line below and comment the include above when VSCode module support improves
// import proxy.v4;

struct Formattable : pro::facade_builder
        ::add_skill<pro::skills::format>
        ::build {
};

int main() {
    pro::proxy<Formattable> p = pro::make_proxy<Formattable>(123);
    std::cout << std::format("{}\n", *p); // 输出 "123"
}
