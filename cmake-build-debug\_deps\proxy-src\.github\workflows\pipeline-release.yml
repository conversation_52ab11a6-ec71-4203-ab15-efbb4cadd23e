name: Proxy-Release

on:
  workflow_dispatch:

jobs:
  draft-release:
    name: Draft release
    permissions:
      contents: write
    runs-on: ubuntu-24.04
    steps:
    - uses: actions/checkout@v4
    - name: pack source
      id: run-pack
      run: |
        version=$(grep -oP 'msft_proxy\d+\s+VERSION\s+\K[0-9]+\.[0-9]+\.[0-9]+' CMakeLists.txt)
        git tag "$version"
        git push origin "$version"
        tar -czf "proxy-$version.tgz" $(git ls-files 'include/**.h' 'include/**.ixx')
        echo "PRO_VER=$version" >> $GITHUB_OUTPUT
      shell: bash

    - name: create release draft
      uses: softprops/action-gh-release@v2
      with:
        draft: true
        tag_name: ${{ steps.run-pack.outputs.PRO_VER }}
        files: proxy-*.tgz
        name: Proxy ${{ steps.run-pack.outputs.PRO_VER }} Release
        generate_release_notes: true
