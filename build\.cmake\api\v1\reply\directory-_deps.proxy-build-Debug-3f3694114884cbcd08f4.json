{"backtraceGraph": {"commands": ["install"], "files": ["build/_deps/proxy-src/CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 58, "parent": 0}, {"command": 0, "file": 0, "line": 63, "parent": 0}, {"command": 0, "file": 0, "line": 87, "parent": 0}]}, "installers": [{"backtrace": 1, "component": "Unspecified", "destination": "include", "fileSetDirectories": ["build/_deps/proxy-src/include"], "fileSetName": "public_headers", "fileSetTarget": {"id": "msft_proxy4::@b1154f979a626d5807af", "index": 0}, "fileSetType": "HEADERS", "paths": [{"from": "build/_deps/proxy-src/include/proxy/proxy.h", "to": "proxy/proxy.h"}, {"from": "build/_deps/proxy-src/include/proxy/proxy_macros.h", "to": "proxy/proxy_macros.h"}, {"from": "build/_deps/proxy-src/include/proxy/proxy_fmt.h", "to": "proxy/proxy_fmt.h"}, {"from": "build/_deps/proxy-src/include/proxy/v4/proxy.ixx", "to": "proxy/v4/proxy.ixx"}, {"from": "build/_deps/proxy-src/include/proxy/v4/proxy.h", "to": "proxy/v4/proxy.h"}, {"from": "build/_deps/proxy-src/include/proxy/v4/proxy_macros.h", "to": "proxy/v4/proxy_macros.h"}, {"from": "build/_deps/proxy-src/include/proxy/v4/proxy_fmt.h", "to": "proxy/v4/proxy_fmt.h"}], "type": "fileSet"}, {"backtrace": 2, "component": "Unspecified", "destination": "share/msft_proxy4", "exportName": "msft_proxy4Targets", "exportTargets": [{"id": "msft_proxy4::@b1154f979a626d5807af", "index": 0}], "paths": ["_deps/proxy-build/CMakeFiles/Export/0d99fc5f7230140cc773567a9c3dbaa5/msft_proxy4Targets.cmake"], "type": "export"}, {"backtrace": 3, "component": "Unspecified", "destination": "share/msft_proxy4", "paths": ["build/_deps/proxy-build/cmake/msft_proxy4Config.cmake", "build/_deps/proxy-build/cmake/msft_proxy4ConfigVersion.cmake"], "type": "file"}], "paths": {"build": "_deps/proxy-build", "source": "build/_deps/proxy-src"}}