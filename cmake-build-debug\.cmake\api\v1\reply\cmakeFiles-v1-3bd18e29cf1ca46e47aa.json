{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "cmake-build-debug/CMakeFiles/4.0.2/CMakeSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Windows-Initialize.cmake"}, {"isGenerated": true, "path": "cmake-build-debug/CMakeFiles/4.0.2/CMakeCCompiler.cmake"}, {"isGenerated": true, "path": "cmake-build-debug/CMakeFiles/4.0.2/CMakeCXXCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Windows.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/WindowsPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Compiler/Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Windows-Clang-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Windows-Clang.cmake"}, {"isGenerated": true, "path": "cmake-build-debug/CMakeFiles/4.0.2/CMakeRCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeRCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Internal/CMakeCLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Linker/LLD-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Linker/LLD.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Linker/MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Linker/Windows-LLD-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Linker/Windows-LLD.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Linker/Windows-MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeCXXInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Compiler/Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Compiler/Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Windows-Clang-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Windows-Clang.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Internal/CMakeCXXLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Internal/CMakeCommonLinkerInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Linker/LLD-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Linker/LLD.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Linker/MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Linker/Windows-LLD-CXX.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Linker/Windows-LLD.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Linker/Windows-MSVC.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/FetchContent.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/ExternalProject/shared_internal_commands.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/FindGit.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/FindPackageHandleStandardArgs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/FindPackageMessage.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/FetchContent/CMakeLists.cmake.in"}, {"isGenerated": true, "path": "cmake-build-debug/_deps/proxy-src/CMakeLists.txt"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/GNUInstallDirs.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakePackageConfigHelpers.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/WriteBasicConfigVersionFile.cmake"}, {"isGenerated": true, "path": "cmake-build-debug/_deps/proxy-src/cmake/msft_proxy4Config.cmake.in"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakePackageConfigHelpers.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/WriteBasicConfigVersionFile.cmake"}, {"isCMake": true, "isExternal": true, "path": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/BasicConfigVersion-SameMajorVersion.cmake.in"}], "kind": "cmakeFiles", "paths": {"build": "C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug", "source": "C:/Users/<USER>/CLionProjects/untitled"}, "version": {"major": 1, "minor": 1}}