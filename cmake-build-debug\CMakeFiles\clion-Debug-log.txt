C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe -DCMAKE_BUILD_TYPE=Debug -DCMAKE_MAKE_PROGRAM=C:/Users/<USER>/AppData/Local/Programs/CLion/bin/ninja/win/x64/ninja.exe -G Ninja -S C:\Users\<USER>\CLionProjects\untitled -B C:\Users\<USER>\CLionProjects\untitled\cmake-build-debug
-- Declaring `msft_proxy4::proxy_module` target for include path `C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-src/include`
-- Configuring done (1.2s)
-- Generating done (0.1s)
-- Build files have been written to: C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug
