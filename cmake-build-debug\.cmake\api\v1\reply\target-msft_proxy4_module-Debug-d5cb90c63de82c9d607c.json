{"archive": {}, "artifacts": [{"path": "msft_proxy4_module.lib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "target_compile_features", "target_sources"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 31, "parent": 0}, {"command": 1, "file": 0, "line": 47, "parent": 0}, {"command": 2, "file": 0, "line": 46, "parent": 0}, {"command": 3, "file": 0, "line": 40, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-O0 -std=gnu++23 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -g -Xclang -gcodeview"}], "includes": [{"backtrace": 2, "path": "C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-src/include"}], "language": "CXX", "languageStandard": {"backtraces": [3], "standard": "23"}, "sourceIndexes": [0]}], "fileSets": [{"baseDirectories": ["cmake-build-debug/_deps/proxy-src/include"], "name": "CXX_MODULES", "type": "CXX_MODULES", "visibility": "PUBLIC"}], "id": "msft_proxy4_module::@6890427a1f51a3e7e1df", "name": "msft_proxy4_module", "nameOnDisk": "msft_proxy4_module.lib", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 4, "compileGroupIndex": 0, "path": "cmake-build-debug/_deps/proxy-src/include/proxy/v4/proxy.ixx", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}