{"sources": [{"file": "C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-subbuild/CMakeFiles/proxy-populate"}, {"file": "C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-subbuild/CMakeFiles/proxy-populate.rule"}, {"file": "C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-subbuild/CMakeFiles/proxy-populate-complete.rule"}, {"file": "C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/proxy-populate-build.rule"}, {"file": "C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/proxy-populate-configure.rule"}, {"file": "C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/proxy-populate-download.rule"}, {"file": "C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/proxy-populate-install.rule"}, {"file": "C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/proxy-populate-mkdir.rule"}, {"file": "C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/proxy-populate-patch.rule"}, {"file": "C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/proxy-populate-test.rule"}, {"file": "C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/proxy-populate-update.rule"}], "target": {"labels": ["proxy-populate"], "name": "proxy-populate"}}