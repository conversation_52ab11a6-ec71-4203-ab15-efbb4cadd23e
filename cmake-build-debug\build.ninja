# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 4.0

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: untitled
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/
# =============================================================================
# Object build statements for STATIC_LIBRARY target msft_proxy4_module


#############################################
# Order-only phony target for msft_proxy4_module

build cmake_object_order_depends_target_msft_proxy4_module: phony || .

build CMakeFiles/msft_proxy4_module.dir/_deps/proxy-src/include/proxy/v4/proxy.ixx.obj.ddi: CXX_SCAN__msft_proxy4_module_Debug C$:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-src/include/proxy/v4/proxy.ixx || cmake_object_order_depends_target_msft_proxy4_module
  DEP_FILE = CMakeFiles\msft_proxy4_module.dir\_deps\proxy-src\include\proxy\v4\proxy.ixx.obj.ddi.d
  DYNDEP_INTERMEDIATE_FILE = CMakeFiles\msft_proxy4_module.dir\_deps\proxy-src\include\proxy\v4\proxy.ixx.obj.ddi
  FLAGS = -O0 -std=gnu++23 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -g -Xclang -gcodeview
  INCLUDES = -IC:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-src/include
  OBJ_FILE = CMakeFiles\msft_proxy4_module.dir\_deps\proxy-src\include\proxy\v4\proxy.ixx.obj
  PREPROCESSED_OUTPUT_FILE = CMakeFiles\msft_proxy4_module.dir\_deps\proxy-src\include\proxy\v4\proxy.ixx.obj.ddi.i

build CMakeFiles/msft_proxy4_module.dir/_deps/proxy-src/include/proxy/v4/proxy.ixx.obj: CXX_COMPILER__msft_proxy4_module_scanned_Debug C$:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-src/include/proxy/v4/proxy.ixx | CMakeFiles/msft_proxy4_module.dir/_deps/proxy-src/include/proxy/v4/proxy.ixx.obj.modmap || cmake_object_order_depends_target_msft_proxy4_module CMakeFiles/msft_proxy4_module.dir/CXX.dd
  CONFIG = Debug
  DEP_FILE = CMakeFiles\msft_proxy4_module.dir\_deps\proxy-src\include\proxy\v4\proxy.ixx.obj.d
  DYNDEP_MODULE_MAP_FILE = CMakeFiles\msft_proxy4_module.dir\_deps\proxy-src\include\proxy\v4\proxy.ixx.obj.modmap
  FLAGS = -O0 -std=gnu++23 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -g -Xclang -gcodeview
  INCLUDES = -IC:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-src/include
  OBJECT_DIR = CMakeFiles\msft_proxy4_module.dir
  OBJECT_FILE_DIR = CMakeFiles\msft_proxy4_module.dir\_deps\proxy-src\include\proxy\v4
  TARGET_COMPILE_PDB = CMakeFiles\msft_proxy4_module.dir\msft_proxy4_module.pdb
  TARGET_PDB = msft_proxy4_module.pdb
  dyndep = CMakeFiles/msft_proxy4_module.dir/CXX.dd

build CMakeFiles/msft_proxy4_module.dir/CXX.dd | C$:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/CMakeFiles/msft_proxy4_module.dir/CXXModules.json CMakeFiles/msft_proxy4_module.dir/_deps/proxy-src/include/proxy/v4/proxy.ixx.obj.modmap: CXX_DYNDEP__msft_proxy4_module_Debug CMakeFiles/msft_proxy4_module.dir/_deps/proxy-src/include/proxy/v4/proxy.ixx.obj.ddi | C$:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/CMakeFiles/msft_proxy4_module.dir/CXXDependInfo.json


# =============================================================================
# Link build statements for STATIC_LIBRARY target msft_proxy4_module


#############################################
# Link the static library msft_proxy4_module.lib

build msft_proxy4_module.lib: CXX_STATIC_LIBRARY_LINKER__msft_proxy4_module_Debug CMakeFiles/msft_proxy4_module.dir/_deps/proxy-src/include/proxy/v4/proxy.ixx.obj
  CONFIG = Debug
  LANGUAGE_COMPILE_FLAGS = -O0 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -g -Xclang -gcodeview
  OBJECT_DIR = CMakeFiles\msft_proxy4_module.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\msft_proxy4_module.dir\msft_proxy4_module.pdb
  TARGET_FILE = msft_proxy4_module.lib
  TARGET_PDB = msft_proxy4_module.pdb

# =============================================================================
# Object build statements for EXECUTABLE target untitled


#############################################
# Order-only phony target for untitled

build cmake_object_order_depends_target_untitled: phony || cmake_object_order_depends_target_msft_proxy4_module

build CMakeFiles/untitled.dir/main.cpp.obj.ddi: CXX_SCAN__untitled_Debug C$:/Users/<USER>/CLionProjects/untitled/main.cpp || cmake_object_order_depends_target_untitled
  DEP_FILE = CMakeFiles\untitled.dir\main.cpp.obj.ddi.d
  DYNDEP_INTERMEDIATE_FILE = CMakeFiles\untitled.dir\main.cpp.obj.ddi
  FLAGS = -O0 -std=gnu++23 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -g -Xclang -gcodeview
  INCLUDES = -IC:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-src/include
  OBJ_FILE = CMakeFiles\untitled.dir\main.cpp.obj
  PREPROCESSED_OUTPUT_FILE = CMakeFiles\untitled.dir\main.cpp.obj.ddi.i

build CMakeFiles/untitled.dir/main.cpp.obj: CXX_COMPILER__untitled_scanned_Debug C$:/Users/<USER>/CLionProjects/untitled/main.cpp | CMakeFiles/untitled.dir/main.cpp.obj.modmap || cmake_object_order_depends_target_untitled CMakeFiles/untitled.dir/CXX.dd
  CONFIG = Debug
  DEP_FILE = CMakeFiles\untitled.dir\main.cpp.obj.d
  DYNDEP_MODULE_MAP_FILE = CMakeFiles\untitled.dir\main.cpp.obj.modmap
  FLAGS = -O0 -std=gnu++23 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -g -Xclang -gcodeview
  INCLUDES = -IC:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-src/include
  OBJECT_DIR = CMakeFiles\untitled.dir
  OBJECT_FILE_DIR = CMakeFiles\untitled.dir
  TARGET_COMPILE_PDB = CMakeFiles\untitled.dir\
  TARGET_PDB = untitled.pdb
  dyndep = CMakeFiles/untitled.dir/CXX.dd

build CMakeFiles/untitled.dir/modules/hello.ixx.obj.ddi: CXX_SCAN__untitled_Debug C$:/Users/<USER>/CLionProjects/untitled/modules/hello.ixx || cmake_object_order_depends_target_untitled
  DEP_FILE = CMakeFiles\untitled.dir\modules\hello.ixx.obj.ddi.d
  DYNDEP_INTERMEDIATE_FILE = CMakeFiles\untitled.dir\modules\hello.ixx.obj.ddi
  FLAGS = -O0 -std=gnu++23 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -g -Xclang -gcodeview
  INCLUDES = -IC:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-src/include
  OBJ_FILE = CMakeFiles\untitled.dir\modules\hello.ixx.obj
  PREPROCESSED_OUTPUT_FILE = CMakeFiles\untitled.dir\modules\hello.ixx.obj.ddi.i

build CMakeFiles/untitled.dir/modules/hello.ixx.obj: CXX_COMPILER__untitled_scanned_Debug C$:/Users/<USER>/CLionProjects/untitled/modules/hello.ixx | CMakeFiles/untitled.dir/modules/hello.ixx.obj.modmap || cmake_object_order_depends_target_untitled CMakeFiles/untitled.dir/CXX.dd
  CONFIG = Debug
  DEP_FILE = CMakeFiles\untitled.dir\modules\hello.ixx.obj.d
  DYNDEP_MODULE_MAP_FILE = CMakeFiles\untitled.dir\modules\hello.ixx.obj.modmap
  FLAGS = -O0 -std=gnu++23 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -g -Xclang -gcodeview
  INCLUDES = -IC:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-src/include
  OBJECT_DIR = CMakeFiles\untitled.dir
  OBJECT_FILE_DIR = CMakeFiles\untitled.dir\modules
  TARGET_COMPILE_PDB = CMakeFiles\untitled.dir\
  TARGET_PDB = untitled.pdb
  dyndep = CMakeFiles/untitled.dir/CXX.dd

build CMakeFiles/untitled.dir/CXX.dd | C$:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/CMakeFiles/untitled.dir/CXXModules.json CMakeFiles/untitled.dir/main.cpp.obj.modmap CMakeFiles/untitled.dir/modules/hello.ixx.obj.modmap: CXX_DYNDEP__untitled_Debug CMakeFiles/untitled.dir/main.cpp.obj.ddi CMakeFiles/untitled.dir/modules/hello.ixx.obj.ddi | C$:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/CMakeFiles/untitled.dir/CXXDependInfo.json C$:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/CMakeFiles/msft_proxy4_module.dir/CXXModules.json


# =============================================================================
# Link build statements for EXECUTABLE target untitled


#############################################
# Link the executable untitled.exe

build untitled.exe: CXX_EXECUTABLE_LINKER__untitled_Debug CMakeFiles/untitled.dir/main.cpp.obj CMakeFiles/untitled.dir/modules/hello.ixx.obj | msft_proxy4_module.lib || msft_proxy4_module.lib
  CONFIG = Debug
  FLAGS = -O0 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -g -Xclang -gcodeview
  LINK_FLAGS = -Xlinker /subsystem:console -fuse-ld=lld-link
  LINK_LIBRARIES = msft_proxy4_module.lib  -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -loldnames
  OBJECT_DIR = CMakeFiles\untitled.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\untitled.dir\
  TARGET_FILE = untitled.exe
  TARGET_IMPLIB = untitled.lib
  TARGET_PDB = untitled.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\CLionProjects\untitled\cmake-build-debug && "C:\Program Files\CMake\bin\cmake-gui.exe" -SC:\Users\<USER>\CLionProjects\untitled -BC:\Users\<USER>\CLionProjects\untitled\cmake-build-debug"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\CLionProjects\untitled\cmake-build-debug && C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\CLionProjects\untitled -BC:\Users\<USER>\CLionProjects\untitled\cmake-build-debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build list_install_components: phony


#############################################
# Utility command for install

build CMakeFiles/install.util: CUSTOM_COMMAND all
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\CLionProjects\untitled\cmake-build-debug && C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe -P cmake_install.cmake"
  DESC = Install the project...
  pool = console
  restat = 1

build install: phony CMakeFiles/install.util


#############################################
# Utility command for install/local

build CMakeFiles/install/local.util: CUSTOM_COMMAND all
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\CLionProjects\untitled\cmake-build-debug && C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake"
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build install/local: phony CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build CMakeFiles/install/strip.util: CUSTOM_COMMAND all
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\CLionProjects\untitled\cmake-build-debug && C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake"
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build install/strip: phony CMakeFiles/install/strip.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/Users/<USER>/CLionProjects/untitled/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build _deps/proxy-build/CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\CLionProjects\untitled\cmake-build-debug\_deps\proxy-build && "C:\Program Files\CMake\bin\cmake-gui.exe" -SC:\Users\<USER>\CLionProjects\untitled -BC:\Users\<USER>\CLionProjects\untitled\cmake-build-debug"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build _deps/proxy-build/edit_cache: phony _deps/proxy-build/CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build _deps/proxy-build/CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\CLionProjects\untitled\cmake-build-debug\_deps\proxy-build && C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\CLionProjects\untitled -BC:\Users\<USER>\CLionProjects\untitled\cmake-build-debug"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build _deps/proxy-build/rebuild_cache: phony _deps/proxy-build/CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build _deps/proxy-build/list_install_components: phony


#############################################
# Utility command for install

build _deps/proxy-build/CMakeFiles/install.util: CUSTOM_COMMAND _deps/proxy-build/all
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\CLionProjects\untitled\cmake-build-debug\_deps\proxy-build && C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe -P cmake_install.cmake"
  DESC = Install the project...
  pool = console
  restat = 1

build _deps/proxy-build/install: phony _deps/proxy-build/CMakeFiles/install.util


#############################################
# Utility command for install/local

build _deps/proxy-build/CMakeFiles/install/local.util: CUSTOM_COMMAND _deps/proxy-build/all
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\CLionProjects\untitled\cmake-build-debug\_deps\proxy-build && C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake"
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build _deps/proxy-build/install/local: phony _deps/proxy-build/CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build _deps/proxy-build/CMakeFiles/install/strip.util: CUSTOM_COMMAND _deps/proxy-build/all
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\CLionProjects\untitled\cmake-build-debug\_deps\proxy-build && C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake"
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build _deps/proxy-build/install/strip: phony _deps/proxy-build/CMakeFiles/install/strip.util

# =============================================================================
# Target aliases.

build msft_proxy4_module: phony msft_proxy4_module.lib

build untitled: phony untitled.exe

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug

build all: phony untitled.exe _deps/proxy-build/all

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-build

build _deps/proxy-build/all: phony

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja C$:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/cmake_install.cmake C$:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-build/cmake_install.cmake: RERUN_CMAKE | C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/BasicConfigVersion-SameMajorVersion.cmake.in C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakePackageConfigHelpers.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeRCInformation.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/ExternalProject/shared_internal_commands.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/FetchContent.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/FetchContent/CMakeLists.cmake.in C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/FindGit.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/FindPackageHandleStandardArgs.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/FindPackageMessage.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/GNUInstallDirs.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Internal/CMakeCLinkerInformation.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Internal/CMakeCXXLinkerInformation.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Internal/CMakeCommonLinkerInformation.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Linker/LLD-C.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Linker/LLD-CXX.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Linker/LLD.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Linker/MSVC.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Linker/Windows-LLD-C.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Linker/Windows-LLD-CXX.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Linker/Windows-LLD.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Linker/Windows-MSVC.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Windows-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Windows-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Windows-Clang.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Windows-Initialize.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Windows.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/WindowsPaths.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/WriteBasicConfigVersionFile.cmake C$:/Users/<USER>/CLionProjects/untitled/CMakeLists.txt CMakeCache.txt CMakeFiles/4.0.2/CMakeCCompiler.cmake CMakeFiles/4.0.2/CMakeCXXCompiler.cmake CMakeFiles/4.0.2/CMakeRCCompiler.cmake CMakeFiles/4.0.2/CMakeSystem.cmake _deps/proxy-src/CMakeLists.txt _deps/proxy-src/cmake/msft_proxy4Config.cmake.in
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/BasicConfigVersion-SameMajorVersion.cmake.in C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeCInformation.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeCXXInformation.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeCommonLanguageInclude.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeGenericSystem.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeInitializeConfigs.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeLanguageInformation.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakePackageConfigHelpers.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeRCInformation.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeSystemSpecificInformation.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/CMakeSystemSpecificInitialize.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Compiler/Clang-C.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Compiler/Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Compiler/Clang.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/ExternalProject/shared_internal_commands.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/FetchContent.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/FetchContent/CMakeLists.cmake.in C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/FindGit.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/FindPackageHandleStandardArgs.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/FindPackageMessage.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/GNUInstallDirs.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Internal/CMakeCLinkerInformation.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Internal/CMakeCXXLinkerInformation.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Internal/CMakeCommonLinkerInformation.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Linker/LLD-C.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Linker/LLD-CXX.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Linker/LLD.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Linker/MSVC.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Linker/Windows-LLD-C.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Linker/Windows-LLD-CXX.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Linker/Windows-LLD.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Linker/Windows-MSVC.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Windows-Clang-C.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Windows-Clang-CXX.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Windows-Clang.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Windows-Initialize.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/Windows.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/Platform/WindowsPaths.cmake C$:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0/Modules/WriteBasicConfigVersionFile.cmake C$:/Users/<USER>/CLionProjects/untitled/CMakeLists.txt CMakeCache.txt CMakeFiles/4.0.2/CMakeCCompiler.cmake CMakeFiles/4.0.2/CMakeCXXCompiler.cmake CMakeFiles/4.0.2/CMakeRCCompiler.cmake CMakeFiles/4.0.2/CMakeSystem.cmake _deps/proxy-src/CMakeLists.txt _deps/proxy-src/cmake/msft_proxy4Config.cmake.in: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
