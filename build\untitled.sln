﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "CMakePredefinedTargets", "CMakePredefinedTargets", "{54E855CD-414C-3E66-9BC4-2F8A576D7297}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{153D4152-46A6-3594-878E-081748A6D1F5}"
	ProjectSection(ProjectDependencies) = postProject
		{6B1C84E0-9F6A-3413-BCA2-A1031F330286} = {6B1C84E0-9F6A-3413-BCA2-A1031F330286}
		{B3D7700C-D518-3CB3-9259-F0C6F16DB435} = {B3D7700C-D518-3CB3-9259-F0C6F16DB435}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{695FB2E6-5C1A-32FA-B1E3-022A67E49471}"
	ProjectSection(ProjectDependencies) = postProject
		{153D4152-46A6-3594-878E-081748A6D1F5} = {153D4152-46A6-3594-878E-081748A6D1F5}
		{6B1C84E0-9F6A-3413-BCA2-A1031F330286} = {6B1C84E0-9F6A-3413-BCA2-A1031F330286}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{6B1C84E0-9F6A-3413-BCA2-A1031F330286}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "msft_proxy4_module", "msft_proxy4_module.vcxproj", "{935CD864-8D96-3BAC-B0A6-8B28E8F058D5}"
	ProjectSection(ProjectDependencies) = postProject
		{6B1C84E0-9F6A-3413-BCA2-A1031F330286} = {6B1C84E0-9F6A-3413-BCA2-A1031F330286}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "untitled", "untitled.vcxproj", "{B3D7700C-D518-3CB3-9259-F0C6F16DB435}"
	ProjectSection(ProjectDependencies) = postProject
		{6B1C84E0-9F6A-3413-BCA2-A1031F330286} = {6B1C84E0-9F6A-3413-BCA2-A1031F330286}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|win32 = Debug|win32
		Release|win32 = Release|win32
		MinSizeRel|win32 = MinSizeRel|win32
		RelWithDebInfo|win32 = RelWithDebInfo|win32
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{153D4152-46A6-3594-878E-081748A6D1F5}.Debug|win32.ActiveCfg = Debug|win32
		{153D4152-46A6-3594-878E-081748A6D1F5}.Debug|win32.Build.0 = Debug|win32
		{153D4152-46A6-3594-878E-081748A6D1F5}.Release|win32.ActiveCfg = Release|win32
		{153D4152-46A6-3594-878E-081748A6D1F5}.Release|win32.Build.0 = Release|win32
		{153D4152-46A6-3594-878E-081748A6D1F5}.MinSizeRel|win32.ActiveCfg = MinSizeRel|win32
		{153D4152-46A6-3594-878E-081748A6D1F5}.MinSizeRel|win32.Build.0 = MinSizeRel|win32
		{153D4152-46A6-3594-878E-081748A6D1F5}.RelWithDebInfo|win32.ActiveCfg = RelWithDebInfo|win32
		{153D4152-46A6-3594-878E-081748A6D1F5}.RelWithDebInfo|win32.Build.0 = RelWithDebInfo|win32
		{695FB2E6-5C1A-32FA-B1E3-022A67E49471}.Debug|win32.ActiveCfg = Debug|win32
		{695FB2E6-5C1A-32FA-B1E3-022A67E49471}.Release|win32.ActiveCfg = Release|win32
		{695FB2E6-5C1A-32FA-B1E3-022A67E49471}.MinSizeRel|win32.ActiveCfg = MinSizeRel|win32
		{695FB2E6-5C1A-32FA-B1E3-022A67E49471}.RelWithDebInfo|win32.ActiveCfg = RelWithDebInfo|win32
		{6B1C84E0-9F6A-3413-BCA2-A1031F330286}.Debug|win32.ActiveCfg = Debug|win32
		{6B1C84E0-9F6A-3413-BCA2-A1031F330286}.Debug|win32.Build.0 = Debug|win32
		{6B1C84E0-9F6A-3413-BCA2-A1031F330286}.Release|win32.ActiveCfg = Release|win32
		{6B1C84E0-9F6A-3413-BCA2-A1031F330286}.Release|win32.Build.0 = Release|win32
		{6B1C84E0-9F6A-3413-BCA2-A1031F330286}.MinSizeRel|win32.ActiveCfg = MinSizeRel|win32
		{6B1C84E0-9F6A-3413-BCA2-A1031F330286}.MinSizeRel|win32.Build.0 = MinSizeRel|win32
		{6B1C84E0-9F6A-3413-BCA2-A1031F330286}.RelWithDebInfo|win32.ActiveCfg = RelWithDebInfo|win32
		{6B1C84E0-9F6A-3413-BCA2-A1031F330286}.RelWithDebInfo|win32.Build.0 = RelWithDebInfo|win32
		{935CD864-8D96-3BAC-B0A6-8B28E8F058D5}.Debug|win32.ActiveCfg = Debug|win32
		{935CD864-8D96-3BAC-B0A6-8B28E8F058D5}.Debug|win32.Build.0 = Debug|win32
		{935CD864-8D96-3BAC-B0A6-8B28E8F058D5}.Release|win32.ActiveCfg = Release|win32
		{935CD864-8D96-3BAC-B0A6-8B28E8F058D5}.Release|win32.Build.0 = Release|win32
		{935CD864-8D96-3BAC-B0A6-8B28E8F058D5}.MinSizeRel|win32.ActiveCfg = MinSizeRel|win32
		{935CD864-8D96-3BAC-B0A6-8B28E8F058D5}.MinSizeRel|win32.Build.0 = MinSizeRel|win32
		{935CD864-8D96-3BAC-B0A6-8B28E8F058D5}.RelWithDebInfo|win32.ActiveCfg = RelWithDebInfo|win32
		{935CD864-8D96-3BAC-B0A6-8B28E8F058D5}.RelWithDebInfo|win32.Build.0 = RelWithDebInfo|win32
		{B3D7700C-D518-3CB3-9259-F0C6F16DB435}.Debug|win32.ActiveCfg = Debug|win32
		{B3D7700C-D518-3CB3-9259-F0C6F16DB435}.Debug|win32.Build.0 = Debug|win32
		{B3D7700C-D518-3CB3-9259-F0C6F16DB435}.Release|win32.ActiveCfg = Release|win32
		{B3D7700C-D518-3CB3-9259-F0C6F16DB435}.Release|win32.Build.0 = Release|win32
		{B3D7700C-D518-3CB3-9259-F0C6F16DB435}.MinSizeRel|win32.ActiveCfg = MinSizeRel|win32
		{B3D7700C-D518-3CB3-9259-F0C6F16DB435}.MinSizeRel|win32.Build.0 = MinSizeRel|win32
		{B3D7700C-D518-3CB3-9259-F0C6F16DB435}.RelWithDebInfo|win32.ActiveCfg = RelWithDebInfo|win32
		{B3D7700C-D518-3CB3-9259-F0C6F16DB435}.RelWithDebInfo|win32.Build.0 = RelWithDebInfo|win32
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{153D4152-46A6-3594-878E-081748A6D1F5} = {54E855CD-414C-3E66-9BC4-2F8A576D7297}
		{695FB2E6-5C1A-32FA-B1E3-022A67E49471} = {54E855CD-414C-3E66-9BC4-2F8A576D7297}
		{6B1C84E0-9F6A-3413-BCA2-A1031F330286} = {54E855CD-414C-3E66-9BC4-2F8A576D7297}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {177649C7-BE78-3596-B992-DCE88FE12C0D}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
