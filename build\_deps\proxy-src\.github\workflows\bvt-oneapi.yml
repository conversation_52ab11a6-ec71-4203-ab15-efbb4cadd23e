on:
  workflow_call:

jobs:
  bvt-oneapi:
    runs-on: ubuntu-24.04
    steps:
    - uses: actions/checkout@v4

    - name: install libc++
      run: |
        sudo apt-get install -y libc++-19-dev libc++abi-19-dev

    - name: install intel oneAPI
      run: |
        sudo wget -qO /etc/apt/trusted.gpg.d/intel-oneapi.asc https://apt.repos.intel.com/intel-gpg-keys/GPG-PUB-KEY-INTEL-SW-PRODUCTS.PUB
        echo "deb https://apt.repos.intel.com/oneapi all main" | sudo tee /etc/apt/sources.list.d/intel-oneapi.list
        sudo apt update
        sudo apt install -y intel-oneapi-compiler-dpcpp-cpp
        source /opt/intel/oneapi/setvars.sh
        echo "PATH=$PATH" >> $GITHUB_ENV
        echo "LD_LIBRARY_PATH=$LD_LIBRARY_PATH" >> $GITHUB_ENV

    - name: check compiler version
      run: |
        icpx --version

    - name: build and run test with oneapi
      run: |
        cmake -B build -GNinja -DCMAKE_CXX_COMPILER=icpx -DCMAKE_CXX_STANDARD=23 -DCMAKE_BUILD_TYPE=Release -DCMAKE_CXX_FLAGS="-stdlib=libc++"
        cmake --build build -j
        ctest --test-dir build -j
        mkdir build/drop
        chmod +x tools/dump_build_env.sh
        ./tools/dump_build_env.sh icpx build/drop/env-info.json

    - name: run benchmarks
      run: |
        build/benchmarks/msft_proxy_benchmarks --benchmark_min_warmup_time=0.1 --benchmark_min_time=0.1s --benchmark_repetitions=30 --benchmark_enable_random_interleaving=true --benchmark_report_aggregates_only=true --benchmark_format=json > build/drop/benchmarking-results.json

    - name: archive benchmarking results
      uses: actions/upload-artifact@v4
      with:
        name: drop-oneapi
        path: build/drop/
