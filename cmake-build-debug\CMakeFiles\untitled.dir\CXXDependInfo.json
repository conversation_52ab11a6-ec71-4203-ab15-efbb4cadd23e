{"bmi-installation": null, "compiler-frontend-variant": "GNU", "compiler-id": "Clang", "compiler-simulate-id": "MSVC", "config": "Debug", "cxx-modules": {"CMakeFiles/untitled.dir/modules/hello.ixx.obj": {"bmi-only": false, "destination": null, "name": "CXX_MODULES", "relative-directory": "modules", "source": "C:/Users/<USER>/CLionProjects/untitled/modules/hello.ixx", "type": "CXX_MODULES", "visibility": "PRIVATE"}}, "database-info": null, "dir-cur-bld": "C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug", "dir-cur-src": "C:/Users/<USER>/CLionProjects/untitled", "dir-top-bld": "C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug", "dir-top-src": "C:/Users/<USER>/CLionProjects/untitled", "exports": [], "forward-modules-from-target-dirs": [], "include-dirs": ["_deps\\proxy-src\\include"], "language": "CXX", "linked-target-dirs": ["C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/CMakeFiles/msft_proxy4_module.dir"], "module-dir": "C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/CMakeFiles/untitled.dir", "sources": {"CMakeFiles/untitled.dir/main.cpp.obj": {"language": "CXX", "source": "C:/Users/<USER>/CLionProjects/untitled/main.cpp"}}}