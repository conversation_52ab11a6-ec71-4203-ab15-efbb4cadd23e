^C:\USERS\<USER>\CLIONPROJECTS\UNTITLED\BUILD\_DEPS\PROXY-SUBBUILD\CMAKEFILES\4A04645D583A1E1DB06D9B3816EB3C60\PROXY-POPULATE-MKDIR.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -Dcfgdir=/Debug -P C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild/proxy-populate-prefix/tmp/proxy-populate-mkdirs.cmake
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/Debug/proxy-populate-mkdir
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\CLIONPROJECTS\UNTITLED\BUILD\_DEPS\PROXY-SUBBUILD\CMAKEFILES\4A04645D583A1E1DB06D9B3816EB3C60\PROXY-POPULATE-DOWNLOAD.RULE
setlocal
cd C:\Users\<USER>\CLionProjects\untitled\build\_deps
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE -P C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild/proxy-populate-prefix/tmp/proxy-populate-gitclone.cmake
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/Debug/proxy-populate-download
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\CLIONPROJECTS\UNTITLED\BUILD\_DEPS\PROXY-SUBBUILD\CMAKEFILES\4A04645D583A1E1DB06D9B3816EB3C60\PROXY-POPULATE-UPDATE.RULE
setlocal
cd C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-src
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -Dcan_fetch=YES -DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE -P C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild/proxy-populate-prefix/tmp/proxy-populate-gitupdate.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\CLIONPROJECTS\UNTITLED\BUILD\_DEPS\PROXY-SUBBUILD\CMAKEFILES\4A04645D583A1E1DB06D9B3816EB3C60\PROXY-POPULATE-PATCH.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/Debug/proxy-populate-patch
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\CLIONPROJECTS\UNTITLED\BUILD\_DEPS\PROXY-SUBBUILD\CMAKEFILES\4A04645D583A1E1DB06D9B3816EB3C60\PROXY-POPULATE-CONFIGURE.RULE
setlocal
cd C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/Debug/proxy-populate-configure
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\CLIONPROJECTS\UNTITLED\BUILD\_DEPS\PROXY-SUBBUILD\CMAKEFILES\4A04645D583A1E1DB06D9B3816EB3C60\PROXY-POPULATE-BUILD.RULE
setlocal
cd C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/Debug/proxy-populate-build
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\CLIONPROJECTS\UNTITLED\BUILD\_DEPS\PROXY-SUBBUILD\CMAKEFILES\4A04645D583A1E1DB06D9B3816EB3C60\PROXY-POPULATE-INSTALL.RULE
setlocal
cd C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/Debug/proxy-populate-install
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\CLIONPROJECTS\UNTITLED\BUILD\_DEPS\PROXY-SUBBUILD\CMAKEFILES\4A04645D583A1E1DB06D9B3816EB3C60\PROXY-POPULATE-TEST.RULE
setlocal
cd C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/Debug/proxy-populate-test
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\CLIONPROJECTS\UNTITLED\BUILD\_DEPS\PROXY-SUBBUILD\CMAKEFILES\F67A4DEEEA6C8A73CE2FBBE9278189AB\PROXY-POPULATE-COMPLETE.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild/CMakeFiles/Debug
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild/CMakeFiles/Debug/proxy-populate-complete
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/Debug/proxy-populate-done
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\CLIONPROJECTS\UNTITLED\BUILD\_DEPS\PROXY-SUBBUILD\CMAKEFILES\87C3FAD2FBFC80C54F282B023BEFD943\PROXY-POPULATE.RULE
setlocal
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\CLIONPROJECTS\UNTITLED\BUILD\_DEPS\PROXY-SUBBUILD\CMAKELISTS.TXT
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild -BC:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild --check-stamp-file C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
