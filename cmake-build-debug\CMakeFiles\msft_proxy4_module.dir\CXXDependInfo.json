{"bmi-installation": null, "compiler-frontend-variant": "GNU", "compiler-id": "Clang", "compiler-simulate-id": "MSVC", "config": "Debug", "cxx-modules": {"CMakeFiles/msft_proxy4_module.dir/_deps/proxy-src/include/proxy/v4/proxy.ixx.obj": {"bmi-only": false, "destination": null, "name": "CXX_MODULES", "relative-directory": "proxy/v4", "source": "C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-src/include/proxy/v4/proxy.ixx", "type": "CXX_MODULES", "visibility": "PUBLIC"}}, "database-info": null, "dir-cur-bld": "C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug", "dir-cur-src": "C:/Users/<USER>/CLionProjects/untitled", "dir-top-bld": "C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug", "dir-top-src": "C:/Users/<USER>/CLionProjects/untitled", "exports": [], "forward-modules-from-target-dirs": [], "include-dirs": ["_deps\\proxy-src\\include"], "language": "CXX", "linked-target-dirs": [], "module-dir": "C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/CMakeFiles/msft_proxy4_module.dir", "sources": {}}