on:
  workflow_call:

jobs:
  bvt-clang:
    runs-on: ubuntu-24.04
    steps:
    - uses: actions/checkout@v4

    - name: install clang
      run: |
        wget https://apt.llvm.org/llvm.sh
        chmod +x llvm.sh
        sudo ./llvm.sh 20
        sudo apt install -y libc++-20-dev clang-format-20

    - name: check compiler version
      run: |
        clang++-20 --version

    - name: build and run test with clang 20
      run: |
        cmake -B build -GNinja -DCMAKE_C_COMPILER=clang-20 -DCMAKE_CXX_COMPILER=clang++-20 -DCMAKE_CXX_FLAGS="-stdlib=libc++" -DCMAKE_CXX_STANDARD=23 -DCMAKE_BUILD_TYPE=Release -DPROXY_BUILD_MODULES=TRUE
        mapfile -t FILES < <(find include tests benchmarks build/examples_from_docs -type f \( -name '*.h' -o -name '*.ixx' -o -name '*.cpp' \))
        echo "Running clang-format on ${#FILES[@]} files: ${FILES[*]}"
        clang-format-20 --dry-run --Werror "${FILES[@]}"
        cmake --build build -j
        ctest --test-dir build -j
        mkdir build/drop
        chmod +x tools/dump_build_env.sh
        ./tools/dump_build_env.sh clang++-20 build/drop/env-info.json

    - name: run benchmarks
      run: |
        build/benchmarks/msft_proxy_benchmarks --benchmark_min_warmup_time=0.1 --benchmark_min_time=0.1s --benchmark_repetitions=30 --benchmark_enable_random_interleaving=true --benchmark_report_aggregates_only=true --benchmark_format=json > build/drop/benchmarking-results.json

    - name: archive benchmarking results
      uses: actions/upload-artifact@v4
      with:
        name: drop-clang
        path: build/drop/
