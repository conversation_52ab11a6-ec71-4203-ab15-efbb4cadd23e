cmake_minimum_required(VERSION 3.28)
project(untitled)

set(CMAKE_CXX_STANDARD 23)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# Fetch proxy library
include(FetchContent)
FetchContent_Declare(
        proxy
        GIT_REPOSITORY https://github.com/microsoft/proxy.git
        GIT_TAG 4.0.0
)
FetchContent_MakeAvailable(proxy)

# 设置 Proxy 的源目录（用于模块声明）
set(msft_proxy4_SOURCE_DIR ${proxy_SOURCE_DIR})
set(msft_proxy4_INCLUDE_DIR ${proxy_SOURCE_DIR}/include)

# 声明 Proxy 的模块目标（基于官方文档的脚本）
if (NOT DEFINED msft_proxy4_INCLUDE_DIR)
    if (NOT DEFINED msft_proxy4_SOURCE_DIR)
        message(FATAL_ERROR "`msft_proxy4_INCLUDE_DIR` or `msft_proxy4_SOURCE_DIR` must be defined.")
    endif ()
    set(msft_proxy4_INCLUDE_DIR ${msft_proxy4_SOURCE_DIR}/include)
endif ()

message(STATUS "Declaring `msft_proxy4::proxy_module` target for include path `${msft_proxy4_INCLUDE_DIR}`")


add_library(msft_proxy4_module)
set_target_properties(
        msft_proxy4_module
        PROPERTIES
        SYSTEM TRUE
        EXCLUDE_FROM_ALL TRUE
)

add_library(msft_proxy4::proxy_module ALIAS msft_proxy4_module)
target_sources(msft_proxy4_module PUBLIC
        FILE_SET CXX_MODULES
        BASE_DIRS ${msft_proxy4_INCLUDE_DIR}
        FILES
        ${msft_proxy4_INCLUDE_DIR}/proxy/v4/proxy.ixx
)
target_compile_features(msft_proxy4_module PUBLIC cxx_std_20)
target_link_libraries(msft_proxy4_module PUBLIC msft_proxy4::proxy)  # 如果有预定义的 proxy target，否则调整

add_executable(untitled)

target_sources(untitled
        PRIVATE
        main.cpp
        PRIVATE FILE_SET CXX_MODULES FILES
        modules/hello.ixx
)

# Link proxy library
# Use header-only version for better VSCode IntelliSense support
target_link_libraries(untitled PRIVATE msft_proxy4::proxy)
# Uncomment the line below and comment the line above when using modules
# target_link_libraries(untitled PRIVATE msft_proxy4::proxy_module)
