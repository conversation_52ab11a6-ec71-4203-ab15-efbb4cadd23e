{"artifacts": [{"path": "untitled.exe"}, {"path": "untitled.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "target_sources"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 49, "parent": 0}, {"command": 1, "file": 0, "line": 60, "parent": 0}, {"command": 2, "file": 0, "line": 51, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-O0 -std=gnu++23 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -g -Xclang -gcodeview"}], "includes": [{"backtrace": 2, "path": "C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-src/include"}], "language": "CXX", "languageStandard": {"backtraces": [2], "standard": "23"}, "sourceIndexes": [0, 1]}], "fileSets": [{"baseDirectories": ["."], "name": "CXX_MODULES", "type": "CXX_MODULES", "visibility": "PRIVATE"}], "id": "untitled::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-O0 -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -g -Xclang -gcodeview", "role": "flags"}, {"fragment": "-Xlinker /subsystem:console", "role": "flags"}, {"fragment": "-fuse-ld=lld-link", "role": "flags"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -loldnames", "role": "libraries"}], "language": "CXX"}, "name": "untitled", "nameOnDisk": "untitled.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}], "sources": [{"backtrace": 3, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "modules/hello.ixx", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}