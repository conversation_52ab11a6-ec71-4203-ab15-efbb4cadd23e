﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|win32">
      <Configuration>Debug</Configuration>
      <Platform>win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{45F8710E-B04F-3D09-8FDD-D9B6EB9344EE}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>win32</Platform>
    <ProjectName>proxy-populate</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|win32'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
    <UseDebugLibraries>true</UseDebugLibraries>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\CMakeFiles\4a04645d583a1e1db06d9b3816eb3c60\proxy-populate-mkdir.rule">
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">Creating directories for 'proxy-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -Dcfgdir=/Debug -P C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild/proxy-populate-prefix/tmp/proxy-populate-mkdirs.cmake
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/Debug/proxy-populate-mkdir
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\proxy-populate-prefix\src\proxy-populate-stamp\Debug\proxy-populate-mkdir</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\CMakeFiles\4a04645d583a1e1db06d9b3816eb3c60\proxy-populate-download.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">Performing download step (git clone) for 'proxy-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">setlocal
cd C:\Users\<USER>\CLionProjects\untitled\build\_deps
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE -P C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild/proxy-populate-prefix/tmp/proxy-populate-gitclone.cmake
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/Debug/proxy-populate-download
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-gitinfo.txt;C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\proxy-populate-prefix\src\proxy-populate-stamp\Debug\proxy-populate-mkdir;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\proxy-populate-prefix\src\proxy-populate-stamp\Debug\proxy-populate-download</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\CMakeFiles\4a04645d583a1e1db06d9b3816eb3c60\proxy-populate-update.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">Performing update step for 'proxy-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">setlocal
cd C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-src
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -Dcan_fetch=YES -DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE -P C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild/proxy-populate-prefix/tmp/proxy-populate-gitupdate.cmake
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\proxy-populate-prefix\tmp\proxy-populate-gitupdate.cmake;C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-update-info.txt;C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\proxy-populate-prefix\src\proxy-populate-stamp\Debug\proxy-populate-download;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\proxy-populate-prefix\src\proxy-populate-stamp\Debug\proxy-populate-update</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\CMakeFiles\4a04645d583a1e1db06d9b3816eb3c60\proxy-populate-patch.rule">
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">No patch step for 'proxy-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/Debug/proxy-populate-patch
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-patch-info.txt;C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\proxy-populate-prefix\src\proxy-populate-stamp\Debug\proxy-populate-update;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\proxy-populate-prefix\src\proxy-populate-stamp\Debug\proxy-populate-patch</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\CMakeFiles\4a04645d583a1e1db06d9b3816eb3c60\proxy-populate-configure.rule">
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">No configure step for 'proxy-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">setlocal
cd C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/Debug/proxy-populate-configure
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\proxy-populate-prefix\tmp\proxy-populate-cfgcmd.txt;C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\proxy-populate-prefix\src\proxy-populate-stamp\Debug\proxy-populate-patch;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\proxy-populate-prefix\src\proxy-populate-stamp\Debug\proxy-populate-configure</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\CMakeFiles\4a04645d583a1e1db06d9b3816eb3c60\proxy-populate-build.rule">
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">No build step for 'proxy-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">setlocal
cd C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/Debug/proxy-populate-build
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\proxy-populate-prefix\src\proxy-populate-stamp\Debug\proxy-populate-configure;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\proxy-populate-prefix\src\proxy-populate-stamp\Debug\proxy-populate-build</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\CMakeFiles\4a04645d583a1e1db06d9b3816eb3c60\proxy-populate-install.rule">
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">No install step for 'proxy-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">setlocal
cd C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/Debug/proxy-populate-install
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\proxy-populate-prefix\src\proxy-populate-stamp\Debug\proxy-populate-build;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\proxy-populate-prefix\src\proxy-populate-stamp\Debug\proxy-populate-install</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\CMakeFiles\4a04645d583a1e1db06d9b3816eb3c60\proxy-populate-test.rule">
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">No test step for 'proxy-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">setlocal
cd C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E echo_append
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/Debug/proxy-populate-test
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\proxy-populate-prefix\src\proxy-populate-stamp\Debug\proxy-populate-install;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\proxy-populate-prefix\src\proxy-populate-stamp\Debug\proxy-populate-test</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\CMakeFiles\f67a4deeea6c8a73ce2fbbe9278189ab\proxy-populate-complete.rule">
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">Completed 'proxy-populate'</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -E make_directory C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild/CMakeFiles/Debug
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild/CMakeFiles/Debug/proxy-populate-complete
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/Debug/proxy-populate-done
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\proxy-populate-prefix\src\proxy-populate-stamp\Debug\proxy-populate-install;C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\proxy-populate-prefix\src\proxy-populate-stamp\Debug\proxy-populate-mkdir;C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\proxy-populate-prefix\src\proxy-populate-stamp\Debug\proxy-populate-download;C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\proxy-populate-prefix\src\proxy-populate-stamp\Debug\proxy-populate-update;C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\proxy-populate-prefix\src\proxy-populate-stamp\Debug\proxy-populate-patch;C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\proxy-populate-prefix\src\proxy-populate-stamp\Debug\proxy-populate-configure;C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\proxy-populate-prefix\src\proxy-populate-stamp\Debug\proxy-populate-build;C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\proxy-populate-prefix\src\proxy-populate-stamp\Debug\proxy-populate-test;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\CMakeFiles\Debug\proxy-populate-complete</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\CMakeFiles\87c3fad2fbfc80c54f282b023befd943\proxy-populate.rule">
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|win32'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">setlocal
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\CMakeFiles\Debug\proxy-populate-complete;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\CMakeFiles\proxy-populate</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">false</LinkObjects>
      <VerifyInputsAndOutputsExist Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">false</VerifyInputsAndOutputsExist>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">Building Custom Rule C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild -BC:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild --check-stamp-file C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-subbuild/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\ExternalProject.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\ExternalProject\PatchInfo.txt.in;C:\Program Files\CMake\share\cmake-4.0\Modules\ExternalProject\RepositoryInfo.txt.in;C:\Program Files\CMake\share\cmake-4.0\Modules\ExternalProject\UpdateInfo.txt.in;C:\Program Files\CMake\share\cmake-4.0\Modules\ExternalProject\cfgcmd.txt.in;C:\Program Files\CMake\share\cmake-4.0\Modules\ExternalProject\gitclone.cmake.in;C:\Program Files\CMake\share\cmake-4.0\Modules\ExternalProject\gitupdate.cmake.in;C:\Program Files\CMake\share\cmake-4.0\Modules\ExternalProject\mkdirs.cmake.in;C:\Program Files\CMake\share\cmake-4.0\Modules\ExternalProject\shared_internal_commands.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake;C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\CMakeFiles\4.0.2\CMakeSystem.cmake;C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\proxy-populate-prefix\tmp\proxy-populate-mkdirs.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|win32'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <None Include="C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\CMakeFiles\proxy-populate">
    </None>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\ZERO_CHECK.vcxproj">
      <Project>{04203653-FB53-38A6-ACE0-3C04795AAE5E}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>