﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "CMakePredefinedTargets", "CMakePredefinedTargets", "{4F1C1147-DB93-33F3-BC33-85E67D27A525}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "ExternalProjectTargets", "ExternalProjectTargets", "{DA26E8CF-A684-3EBF-B838-8702A7CDE773}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "proxy-populate", "ExternalProjectTargets\proxy-populate", "{8BE39C45-1440-3BCB-A186-DDB415369F2A}"
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{6B055209-C019-3901-BA8E-1AF69F2BD346}"
	ProjectSection(ProjectDependencies) = postProject
		{04203653-FB53-38A6-ACE0-3C04795AAE5E} = {04203653-FB53-38A6-ACE0-3C04795AAE5E}
		{45F8710E-B04F-3D09-8FDD-D9B6EB9344EE} = {45F8710E-B04F-3D09-8FDD-D9B6EB9344EE}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "ZERO_CHECK.vcxproj", "{04203653-FB53-38A6-ACE0-3C04795AAE5E}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "proxy-populate", "proxy-populate.vcxproj", "{45F8710E-B04F-3D09-8FDD-D9B6EB9344EE}"
	ProjectSection(ProjectDependencies) = postProject
		{04203653-FB53-38A6-ACE0-3C04795AAE5E} = {04203653-FB53-38A6-ACE0-3C04795AAE5E}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|win32 = Debug|win32
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{6B055209-C019-3901-BA8E-1AF69F2BD346}.Debug|win32.ActiveCfg = Debug|win32
		{04203653-FB53-38A6-ACE0-3C04795AAE5E}.Debug|win32.ActiveCfg = Debug|win32
		{04203653-FB53-38A6-ACE0-3C04795AAE5E}.Debug|win32.Build.0 = Debug|win32
		{45F8710E-B04F-3D09-8FDD-D9B6EB9344EE}.Debug|win32.ActiveCfg = Debug|win32
		{45F8710E-B04F-3D09-8FDD-D9B6EB9344EE}.Debug|win32.Build.0 = Debug|win32
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{6B055209-C019-3901-BA8E-1AF69F2BD346} = {4F1C1147-DB93-33F3-BC33-85E67D27A525}
		{04203653-FB53-38A6-ACE0-3C04795AAE5E} = {4F1C1147-DB93-33F3-BC33-85E67D27A525}
		{8BE39C45-1440-3BCB-A186-DDB415369F2A} = {DA26E8CF-A684-3EBF-B838-8702A7CDE773}
		{45F8710E-B04F-3D09-8FDD-D9B6EB9344EE} = {8BE39C45-1440-3BCB-A186-DDB415369F2A}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {28BF85C2-363F-3F0D-9A65-4ADB6E9EBD4C}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal
