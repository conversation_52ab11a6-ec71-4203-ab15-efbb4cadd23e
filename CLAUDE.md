# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a C++23 project using modern C++ modules. It's built with CMake and appears to be developed in CLion.

## Build System

The project uses CMake 4.0+ with <PERSON> as the build system generator.

### Build Commands

```bash
# Configure the project
cmake -B cmake-build-debug -G Ninja

# Build the project
cmake --build cmake-build-debug

# Run the executable
./cmake-build-debug/untitled
```

## Architecture

The project uses C++23 modules:
- `main.cpp`: Entry point that imports the standard library module (`std`) and a custom `hello` module
- `modules/hello.ixx`: Custom module interface unit

The project demonstrates modern C++ module usage with the `import std;` directive for accessing the standard library as a module.