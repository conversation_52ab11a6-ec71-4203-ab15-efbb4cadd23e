{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "hasInstallRule": true, "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.28"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 2, 3, 4]}, {"build": "_deps/proxy-build", "hasInstallRule": true, "jsonFile": "directory-_deps.proxy-build-Debug-3f3694114884cbcd08f4.json", "minimumCMakeVersion": {"string": "3.28"}, "parentIndex": 0, "projectIndex": 1, "source": "build/_deps/proxy-src", "targetIndexes": [1]}], "name": "Debug", "projects": [{"childIndexes": [1], "directoryIndexes": [0], "name": "untitled", "targetIndexes": [0, 2, 3, 4]}, {"directoryIndexes": [1], "name": "msft_proxy4", "parentIndex": 0, "targetIndexes": [1]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Debug-04ce4e0d8196470f57ab.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 1, "id": "ALL_BUILD::@b1154f979a626d5807af", "jsonFile": "target-ALL_BUILD-Debug-98b02a5a9a4ef03176a4.json", "name": "ALL_BUILD", "projectIndex": 1}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Debug-ade42129d2aa970c9fa3.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "msft_proxy4_module::@6890427a1f51a3e7e1df", "jsonFile": "target-msft_proxy4_module-Debug-849c50aa3ce8bb653fed.json", "name": "msft_proxy4_module", "projectIndex": 0}, {"directoryIndex": 0, "id": "untitled::@6890427a1f51a3e7e1df", "jsonFile": "target-untitled-Debug-0253357afe47ccf94a2c.json", "name": "untitled", "projectIndex": 0}]}, {"directories": [{"build": ".", "childIndexes": [1], "hasInstallRule": true, "jsonFile": "directory-.-Release-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.28"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 2, 3, 4]}, {"build": "_deps/proxy-build", "hasInstallRule": true, "jsonFile": "directory-_deps.proxy-build-Release-3f3694114884cbcd08f4.json", "minimumCMakeVersion": {"string": "3.28"}, "parentIndex": 0, "projectIndex": 1, "source": "build/_deps/proxy-src", "targetIndexes": [1]}], "name": "Release", "projects": [{"childIndexes": [1], "directoryIndexes": [0], "name": "untitled", "targetIndexes": [0, 2, 3, 4]}, {"directoryIndexes": [1], "name": "msft_proxy4", "parentIndex": 0, "targetIndexes": [1]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-Release-04ce4e0d8196470f57ab.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 1, "id": "ALL_BUILD::@b1154f979a626d5807af", "jsonFile": "target-ALL_BUILD-Release-98b02a5a9a4ef03176a4.json", "name": "ALL_BUILD", "projectIndex": 1}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-Release-ade42129d2aa970c9fa3.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "msft_proxy4_module::@6890427a1f51a3e7e1df", "jsonFile": "target-msft_proxy4_module-Release-d6f0cd1515084e94cac5.json", "name": "msft_proxy4_module", "projectIndex": 0}, {"directoryIndex": 0, "id": "untitled::@6890427a1f51a3e7e1df", "jsonFile": "target-untitled-Release-22bac2abedd61c902f16.json", "name": "untitled", "projectIndex": 0}]}, {"directories": [{"build": ".", "childIndexes": [1], "hasInstallRule": true, "jsonFile": "directory-.-MinSizeRel-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.28"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 2, 3, 4]}, {"build": "_deps/proxy-build", "hasInstallRule": true, "jsonFile": "directory-_deps.proxy-build-MinSizeRel-3f3694114884cbcd08f4.json", "minimumCMakeVersion": {"string": "3.28"}, "parentIndex": 0, "projectIndex": 1, "source": "build/_deps/proxy-src", "targetIndexes": [1]}], "name": "MinSizeRel", "projects": [{"childIndexes": [1], "directoryIndexes": [0], "name": "untitled", "targetIndexes": [0, 2, 3, 4]}, {"directoryIndexes": [1], "name": "msft_proxy4", "parentIndex": 0, "targetIndexes": [1]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-MinSizeRel-04ce4e0d8196470f57ab.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 1, "id": "ALL_BUILD::@b1154f979a626d5807af", "jsonFile": "target-ALL_BUILD-MinSizeRel-98b02a5a9a4ef03176a4.json", "name": "ALL_BUILD", "projectIndex": 1}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-MinSizeRel-ade42129d2aa970c9fa3.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "msft_proxy4_module::@6890427a1f51a3e7e1df", "jsonFile": "target-msft_proxy4_module-MinSizeRel-0752aee5422f7b7f9a00.json", "name": "msft_proxy4_module", "projectIndex": 0}, {"directoryIndex": 0, "id": "untitled::@6890427a1f51a3e7e1df", "jsonFile": "target-untitled-MinSizeRel-8db1b36c728ebda52abe.json", "name": "untitled", "projectIndex": 0}]}, {"directories": [{"build": ".", "childIndexes": [1], "hasInstallRule": true, "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.28"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 2, 3, 4]}, {"build": "_deps/proxy-build", "hasInstallRule": true, "jsonFile": "directory-_deps.proxy-build-RelWithDebInfo-3f3694114884cbcd08f4.json", "minimumCMakeVersion": {"string": "3.28"}, "parentIndex": 0, "projectIndex": 1, "source": "build/_deps/proxy-src", "targetIndexes": [1]}], "name": "RelWithDebInfo", "projects": [{"childIndexes": [1], "directoryIndexes": [0], "name": "untitled", "targetIndexes": [0, 2, 3, 4]}, {"directoryIndexes": [1], "name": "msft_proxy4", "parentIndex": 0, "targetIndexes": [1]}], "targets": [{"directoryIndex": 0, "id": "ALL_BUILD::@6890427a1f51a3e7e1df", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-04ce4e0d8196470f57ab.json", "name": "ALL_BUILD", "projectIndex": 0}, {"directoryIndex": 1, "id": "ALL_BUILD::@b1154f979a626d5807af", "jsonFile": "target-ALL_BUILD-RelWithDebInfo-98b02a5a9a4ef03176a4.json", "name": "ALL_BUILD", "projectIndex": 1}, {"directoryIndex": 0, "id": "ZERO_CHECK::@6890427a1f51a3e7e1df", "jsonFile": "target-ZERO_CHECK-RelWithDebInfo-ade42129d2aa970c9fa3.json", "name": "ZERO_CHECK", "projectIndex": 0}, {"directoryIndex": 0, "id": "msft_proxy4_module::@6890427a1f51a3e7e1df", "jsonFile": "target-msft_proxy4_module-RelWithDebInfo-9afee43b1e6bfbd6b470.json", "name": "msft_proxy4_module", "projectIndex": 0}, {"directoryIndex": 0, "id": "untitled::@6890427a1f51a3e7e1df", "jsonFile": "target-untitled-RelWithDebInfo-859aaff8c996a5e81af6.json", "name": "untitled", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/CLionProjects/untitled/build", "source": "C:/Users/<USER>/CLionProjects/untitled"}, "version": {"major": 2, "minor": 8}}