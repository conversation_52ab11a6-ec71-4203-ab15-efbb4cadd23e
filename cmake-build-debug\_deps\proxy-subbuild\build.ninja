# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 4.0

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: proxy-populate
# Configurations: 
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles\rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:\Users\yanglin\CLionProjects\untitled\cmake-build-debug\_deps\proxy-subbuild\

#############################################
# Utility command for proxy-populate

build proxy-populate: phony CMakeFiles\proxy-populate CMakeFiles\proxy-populate-complete proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-done proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-build proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-configure proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-download proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-install proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-mkdir proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-patch proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-test proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-update


#############################################
# Utility command for edit_cache

build CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\CLionProjects\untitled\cmake-build-debug\_deps\proxy-subbuild && "C:\Program Files\CMake\bin\cmake-gui.exe" -SC:\Users\<USER>\CLionProjects\untitled\cmake-build-debug\_deps\proxy-subbuild -BC:\Users\<USER>\CLionProjects\untitled\cmake-build-debug\_deps\proxy-subbuild"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\CLionProjects\untitled\cmake-build-debug\_deps\proxy-subbuild && C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\CLionProjects\untitled\cmake-build-debug\_deps\proxy-subbuild -BC:\Users\<USER>\CLionProjects\untitled\cmake-build-debug\_deps\proxy-subbuild"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles\rebuild_cache.util


#############################################
# Phony custom command for CMakeFiles\proxy-populate

build CMakeFiles\proxy-populate | ${cmake_ninja_workdir}CMakeFiles\proxy-populate: phony CMakeFiles\proxy-populate-complete


#############################################
# Custom command for CMakeFiles\proxy-populate-complete

build CMakeFiles\proxy-populate-complete proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-done | ${cmake_ninja_workdir}CMakeFiles\proxy-populate-complete ${cmake_ninja_workdir}proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-done: CUSTOM_COMMAND proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-install proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-mkdir proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-download proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-update proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-patch proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-configure proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-build proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-install proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-test
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\CLionProjects\untitled\cmake-build-debug\_deps\proxy-subbuild && C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe -E make_directory C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-subbuild/CMakeFiles && C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe -E touch C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-subbuild/CMakeFiles/proxy-populate-complete && C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe -E touch C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/proxy-populate-done"
  DESC = Completed 'proxy-populate'
  restat = 1


#############################################
# Custom command for proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-build

build proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-build | ${cmake_ninja_workdir}proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-build: CUSTOM_COMMAND proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-configure
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\CLionProjects\untitled\cmake-build-debug\_deps\proxy-build && C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe -E echo_append && C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe -E touch C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/proxy-populate-build"
  DESC = No build step for 'proxy-populate'
  restat = 1


#############################################
# Custom command for proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-configure

build proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-configure | ${cmake_ninja_workdir}proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-configure: CUSTOM_COMMAND proxy-populate-prefix\tmp\proxy-populate-cfgcmd.txt proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-patch
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\CLionProjects\untitled\cmake-build-debug\_deps\proxy-build && C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe -E echo_append && C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe -E touch C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/proxy-populate-configure"
  DESC = No configure step for 'proxy-populate'
  restat = 1


#############################################
# Custom command for proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-download

build proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-download | ${cmake_ninja_workdir}proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-download: CUSTOM_COMMAND proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-gitinfo.txt proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-mkdir
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\CLionProjects\untitled\cmake-build-debug\_deps && C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe -DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE -P C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-subbuild/proxy-populate-prefix/tmp/proxy-populate-gitclone.cmake && C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe -E touch C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/proxy-populate-download"
  DESC = Performing download step (git clone) for 'proxy-populate'
  pool = console
  restat = 1


#############################################
# Custom command for proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-install

build proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-install | ${cmake_ninja_workdir}proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-install: CUSTOM_COMMAND proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-build
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\CLionProjects\untitled\cmake-build-debug\_deps\proxy-build && C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe -E echo_append && C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe -E touch C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/proxy-populate-install"
  DESC = No install step for 'proxy-populate'
  restat = 1


#############################################
# Custom command for proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-mkdir

build proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-mkdir | ${cmake_ninja_workdir}proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-mkdir: CUSTOM_COMMAND
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\CLionProjects\untitled\cmake-build-debug\_deps\proxy-subbuild && C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe -Dcfgdir= -P C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-subbuild/proxy-populate-prefix/tmp/proxy-populate-mkdirs.cmake && C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe -E touch C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/proxy-populate-mkdir"
  DESC = Creating directories for 'proxy-populate'
  restat = 1


#############################################
# Custom command for proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-patch

build proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-patch | ${cmake_ninja_workdir}proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-patch: CUSTOM_COMMAND proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-patch-info.txt proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-update
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\CLionProjects\untitled\cmake-build-debug\_deps\proxy-subbuild && C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe -E echo_append && C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe -E touch C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/proxy-populate-patch"
  DESC = No patch step for 'proxy-populate'
  pool = console
  restat = 1


#############################################
# Custom command for proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-test

build proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-test | ${cmake_ninja_workdir}proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-test: CUSTOM_COMMAND proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-install
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\CLionProjects\untitled\cmake-build-debug\_deps\proxy-build && C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe -E echo_append && C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe -E touch C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-subbuild/proxy-populate-prefix/src/proxy-populate-stamp/proxy-populate-test"
  DESC = No test step for 'proxy-populate'
  restat = 1


#############################################
# Custom command for proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-update

build proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-update | ${cmake_ninja_workdir}proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-update: CUSTOM_COMMAND proxy-populate-prefix\tmp\proxy-populate-gitupdate.cmake proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-update-info.txt proxy-populate-prefix\src\proxy-populate-stamp\proxy-populate-download
  COMMAND = C:\Windows\system32\cmd.exe /C "cd /D C:\Users\<USER>\CLionProjects\untitled\cmake-build-debug\_deps\proxy-src && C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe -Dcan_fetch=YES -DCMAKE_MESSAGE_LOG_LEVEL=VERBOSE -P C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-subbuild/proxy-populate-prefix/tmp/proxy-populate-gitupdate.cmake"
  DESC = Performing update step for 'proxy-populate'
  pool = console

# =============================================================================
# Target aliases.

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-subbuild

build codegen: phony

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug/_deps/proxy-subbuild

build all: phony proxy-populate

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja C$:\Users\yanglin\CLionProjects\untitled\cmake-build-debug\_deps\proxy-subbuild\cmake_install.cmake: RERUN_CMAKE | C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeGenericSystem.cmake C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject.cmake C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\PatchInfo.txt.in C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\RepositoryInfo.txt.in C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\UpdateInfo.txt.in C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\cfgcmd.txt.in C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\gitclone.cmake.in C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\gitupdate.cmake.in C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\mkdirs.cmake.in C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\shared_internal_commands.cmake C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows.cmake C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake CMakeCache.txt CMakeFiles\4.0.2\CMakeSystem.cmake CMakeLists.txt proxy-populate-prefix\tmp\proxy-populate-mkdirs.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeGenericSystem.cmake C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeInitializeConfigs.cmake C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeSystemSpecificInformation.cmake C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\CMakeSystemSpecificInitialize.cmake C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject.cmake C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\PatchInfo.txt.in C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\RepositoryInfo.txt.in C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\UpdateInfo.txt.in C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\cfgcmd.txt.in C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\gitclone.cmake.in C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\gitupdate.cmake.in C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\mkdirs.cmake.in C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\ExternalProject\shared_internal_commands.cmake C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows-Initialize.cmake C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\Windows.cmake C$:\Users\yanglin\AppData\Local\Programs\CLion\bin\cmake\win\x64\share\cmake-4.0\Modules\Platform\WindowsPaths.cmake CMakeCache.txt CMakeFiles\4.0.2\CMakeSystem.cmake CMakeLists.txt proxy-populate-prefix\tmp\proxy-populate-mkdirs.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
