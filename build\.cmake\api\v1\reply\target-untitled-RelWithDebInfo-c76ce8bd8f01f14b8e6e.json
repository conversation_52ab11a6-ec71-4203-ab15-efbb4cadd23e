{"artifacts": [{"path": "RelWithDebInfo/untitled.exe"}, {"path": "RelWithDebInfo/untitled.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "target_sources"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 49, "parent": 0}, {"command": 1, "file": 0, "line": 60, "parent": 0}, {"command": 2, "file": 0, "line": 51, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /EHsc /O2 /Ob1 /DNDEBUG -std:c++latest -MD -Zi"}], "includes": [{"backtrace": 2, "path": "C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-src/include"}], "language": "CXX", "languageStandard": {"backtraces": [2], "standard": "23"}, "sourceIndexes": [0, 1]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "fileSets": [{"baseDirectories": ["."], "name": "CXX_MODULES", "type": "CXX_MODULES", "visibility": "PRIVATE"}], "id": "untitled::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /EHsc /O2 /Ob1 /DNDEBUG -MD -Zi", "role": "flags"}, {"fragment": "/machine:X86 /debug /INCREMENTAL", "role": "flags"}, {"fragment": "/subsystem:console", "role": "flags"}, {"fragment": "kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib", "role": "libraries"}], "language": "CXX"}, "name": "untitled", "nameOnDisk": "untitled.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 1]}], "sources": [{"backtrace": 3, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 3, "compileGroupIndex": 0, "path": "modules/hello.ixx", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}