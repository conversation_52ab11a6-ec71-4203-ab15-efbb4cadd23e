{"configurations": [{"directories": [{"build": ".", "childIndexes": [1], "hasInstallRule": true, "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.28"}, "projectIndex": 0, "source": ".", "targetIndexes": [0, 1]}, {"build": "_deps/proxy-build", "hasInstallRule": true, "jsonFile": "directory-_deps.proxy-build-Debug-cb5dc548787561ef57db.json", "minimumCMakeVersion": {"string": "3.28"}, "parentIndex": 0, "projectIndex": 1, "source": "cmake-build-debug/_deps/proxy-src"}], "name": "Debug", "projects": [{"childIndexes": [1], "directoryIndexes": [0], "name": "untitled", "targetIndexes": [0, 1]}, {"directoryIndexes": [1], "name": "msft_proxy4", "parentIndex": 0}], "targets": [{"directoryIndex": 0, "id": "msft_proxy4_module::@6890427a1f51a3e7e1df", "jsonFile": "target-msft_proxy4_module-Debug-d5cb90c63de82c9d607c.json", "name": "msft_proxy4_module", "projectIndex": 0}, {"directoryIndex": 0, "id": "untitled::@6890427a1f51a3e7e1df", "jsonFile": "target-untitled-Debug-35d9c8958803f55fa8fa.json", "name": "untitled", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "C:/Users/<USER>/CLionProjects/untitled/cmake-build-debug", "source": "C:/Users/<USER>/CLionProjects/untitled"}, "version": {"major": 2, "minor": 8}}