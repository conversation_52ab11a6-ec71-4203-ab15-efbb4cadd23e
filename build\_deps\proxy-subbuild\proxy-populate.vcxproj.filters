﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\CMakeFiles\4a04645d583a1e1db06d9b3816eb3c60\proxy-populate-mkdir.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\CMakeFiles\4a04645d583a1e1db06d9b3816eb3c60\proxy-populate-download.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\CMakeFiles\4a04645d583a1e1db06d9b3816eb3c60\proxy-populate-update.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\CMakeFiles\4a04645d583a1e1db06d9b3816eb3c60\proxy-populate-patch.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\CMakeFiles\4a04645d583a1e1db06d9b3816eb3c60\proxy-populate-configure.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\CMakeFiles\4a04645d583a1e1db06d9b3816eb3c60\proxy-populate-build.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\CMakeFiles\4a04645d583a1e1db06d9b3816eb3c60\proxy-populate-install.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\CMakeFiles\4a04645d583a1e1db06d9b3816eb3c60\proxy-populate-test.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\CMakeFiles\f67a4deeea6c8a73ce2fbbe9278189ab\proxy-populate-complete.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\CMakeFiles\87c3fad2fbfc80c54f282b023befd943\proxy-populate.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="C:\Users\<USER>\CLionProjects\untitled\build\_deps\proxy-subbuild\CMakeFiles\proxy-populate" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{58BE27DE-5EE7-3428-ACDC-1DEF4869D967}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
