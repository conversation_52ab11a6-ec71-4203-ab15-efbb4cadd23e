# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 4.0

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: untitled
# Configurations: Debug
# =============================================================================
# =============================================================================

#############################################
# Rule for generating CXX dependencies.

rule CXX_SCAN__msft_proxy4_module_Debug
  depfile = $DEP_FILE
  command = C:\Windows\system32\cmd.exe /C ""C:/Program Files/LLVM/bin/clang-scan-deps.exe" -format=p1689 -- C:\PROGRA~1\LLVM\bin\clang++.exe $DEFINES $INCLUDES $FLAGS -x c++ $in -c -o $OBJ_FILE -resource-dir "C:/Program Files/LLVM/lib/clang/20" -MT $DYNDEP_INTERMEDIATE_FILE -MD -MF $DEP_FILE > $DYNDEP_INTERMEDIATE_FILE.tmp && "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/bin/cmake.exe" -E rename $DYNDEP_INTERMEDIATE_FILE.tmp $DYNDEP_INTERMEDIATE_FILE"
  description = Scanning $in for CXX dependencies


#############################################
# Rule to generate ninja dyndep files for CXX.

rule CXX_DYNDEP__msft_proxy4_module_Debug
  command = C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe -E cmake_ninja_dyndep --tdi=CMakeFiles\msft_proxy4_module.dir\CXXDependInfo.json --lang=CXX --modmapfmt=clang --dd=$out @$out.rsp
  description = Generating CXX dyndep file $out
  rspfile = $out.rsp
  rspfile_content = $in
  restat = 1


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__msft_proxy4_module_scanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\PROGRA~1\LLVM\bin\clang++.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE @$DYNDEP_MODULE_MAP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__msft_proxy4_module_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\PROGRA~1\LLVM\bin\clang++.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__msft_proxy4_module_Debug
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe -E rm -f $TARGET_FILE && C:\PROGRA~1\LLVM\bin\llvm-ar.exe qc $TARGET_FILE $LINK_FLAGS $in && C:\PROGRA~1\LLVM\bin\llvm-ranlib.exe $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for generating CXX dependencies.

rule CXX_SCAN__untitled_Debug
  depfile = $DEP_FILE
  command = C:\Windows\system32\cmd.exe /C ""C:/Program Files/LLVM/bin/clang-scan-deps.exe" -format=p1689 -- C:\PROGRA~1\LLVM\bin\clang++.exe $DEFINES $INCLUDES $FLAGS -x c++ $in -c -o $OBJ_FILE -resource-dir "C:/Program Files/LLVM/lib/clang/20" -MT $DYNDEP_INTERMEDIATE_FILE -MD -MF $DEP_FILE > $DYNDEP_INTERMEDIATE_FILE.tmp && "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/bin/cmake.exe" -E rename $DYNDEP_INTERMEDIATE_FILE.tmp $DYNDEP_INTERMEDIATE_FILE"
  description = Scanning $in for CXX dependencies


#############################################
# Rule to generate ninja dyndep files for CXX.

rule CXX_DYNDEP__untitled_Debug
  command = C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe -E cmake_ninja_dyndep --tdi=CMakeFiles\untitled.dir\CXXDependInfo.json --lang=CXX --modmapfmt=clang --dd=$out @$out.rsp
  description = Generating CXX dyndep file $out
  rspfile = $out.rsp
  rspfile_content = $in
  restat = 1


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__untitled_scanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\PROGRA~1\LLVM\bin\clang++.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE @$DYNDEP_MODULE_MAP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__untitled_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = ${LAUNCHER}${CODE_CHECK}C:\PROGRA~1\LLVM\bin\clang++.exe $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX executable.

rule CXX_EXECUTABLE_LINKER__untitled_Debug
  command = C:\Windows\system32\cmd.exe /C "$PRE_LINK && C:\PROGRA~1\LLVM\bin\clang++.exe -nostartfiles -nostdlib $FLAGS $LINK_FLAGS $in -o $TARGET_FILE -Xlinker /MANIFEST:EMBED -Xlinker /implib:$TARGET_IMPLIB -Xlinker /pdb:$TARGET_PDB -Xlinker /version:0.0  $LINK_PATH $LINK_LIBRARIES $MANIFESTS && $POST_BUILD"
  description = Linking CXX executable $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = C:\Users\<USER>\AppData\Local\Programs\CLion\bin\cmake\win\x64\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\CLionProjects\untitled -BC:\Users\<USER>\CLionProjects\untitled\cmake-build-debug
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = C:\Users\<USER>\AppData\Local\Programs\CLion\bin\ninja\win\x64\ninja.exe $FILE_ARG -t clean $TARGETS
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = C:\Users\<USER>\AppData\Local\Programs\CLion\bin\ninja\win\x64\ninja.exe -t targets
  description = All primary targets available:

