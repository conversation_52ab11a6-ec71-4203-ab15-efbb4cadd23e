{"TargetName": "`proxy`", "YellowIndicatorThreshold": 5.0, "OutputPath": "benchmarking-report.md", "Environments": [{"Description": "MSVC on Windows", "InfoPath": "artifacts/drop-msvc/env-info.json", "BenchmarkingResultsPath": "artifacts/drop-msvc/benchmarking-results.json"}, {"Description": "GCC on Ubuntu", "InfoPath": "artifacts/drop-gcc/env-info.json", "BenchmarkingResultsPath": "artifacts/drop-gcc/benchmarking-results.json"}, {"Description": "Clang on Ubuntu", "InfoPath": "artifacts/drop-clang/env-info.json", "BenchmarkingResultsPath": "artifacts/drop-clang/benchmarking-results.json"}, {"Description": "Apple Clang on macOS", "InfoPath": "artifacts/drop-appleclang/env-info.json", "BenchmarkingResultsPath": "artifacts/drop-appleclang/benchmarking-results.json"}, {"Description": "NVIDIA HPC on Ubuntu", "InfoPath": "artifacts/drop-nvhpc/env-info.json", "BenchmarkingResultsPath": "artifacts/drop-nvhpc/benchmarking-results.json"}, {"Description": "Intel oneAPI on Ubuntu", "InfoPath": "artifacts/drop-oneapi/env-info.json", "BenchmarkingResultsPath": "artifacts/drop-oneapi/benchmarking-results.json"}], "Metrics": [{"Name": "Indirect invocation on small objects via `proxy` vs. virtual functions", "TargetBenchmarkName": "BM_SmallObjectInvocationViaProxy", "BaselineBenchmarkName": "BM_SmallObjectInvocationViaVirtualFunction"}, {"Name": "Indirect invocation on small objects via `proxy_view` vs. virtual functions", "TargetBenchmarkName": "BM_SmallObjectInvocationViaProxyView", "BaselineBenchmarkName": "BM_SmallObjectInvocationViaVirtualFunction"}, {"Name": "Indirect invocation on small objects via `proxy` vs. virtual functions (shared ownership)", "TargetBenchmarkName": "BM_SmallObjectInvocationViaProxy_Shared", "BaselineBenchmarkName": "BM_SmallObjectInvocationViaVirtualFunction_Shared"}, {"Name": "Indirect invocation on large objects via `proxy` vs. virtual functions", "TargetBenchmarkName": "BM_LargeObjectInvocationViaProxy", "BaselineBenchmarkName": "BM_LargeObjectInvocationViaVirtualFunction"}, {"Name": "Indirect invocation on large objects via `proxy_view` vs. virtual functions", "TargetBenchmarkName": "BM_LargeObjectInvocationViaProxyView", "BaselineBenchmarkName": "BM_LargeObjectInvocationViaVirtualFunction"}, {"Name": "Indirect invocation on large objects via `proxy` vs. virtual functions (shared ownership)", "TargetBenchmarkName": "BM_LargeObjectInvocationViaProxy_Shared", "BaselineBenchmarkName": "BM_LargeObjectInvocationViaVirtualFunction_Shared"}, {"Name": "Basic lifetime management for small objects with `proxy` vs. `std::unique_ptr`", "TargetBenchmarkName": "BM_SmallObjectManagementWithProxy", "BaselineBenchmarkName": "BM_SmallObjectManagementWithUniquePtr"}, {"Name": "Basic lifetime management for small objects with `proxy` (exclusive ownership) vs. `std::shared_ptr` (without memory pool)", "TargetBenchmarkName": "BM_SmallObjectManagementWithProxy", "BaselineBenchmarkName": "BM_SmallObjectManagementWithSharedPtr"}, {"Name": "Basic lifetime management for small objects with `proxy` (exclusive ownership) vs. `std::shared_ptr` (with memory pool)", "TargetBenchmarkName": "BM_SmallObjectManagementWithProxy", "BaselineBenchmarkName": "BM_SmallObjectManagementWithSharedPtr_Pooled"}, {"Name": "Basic lifetime management for small objects with `proxy` (shared ownership) vs. `std::shared_ptr` (both without memory pool)", "TargetBenchmarkName": "BM_SmallObjectManagementWithProxy_Shared", "BaselineBenchmarkName": "BM_SmallObjectManagementWithSharedPtr"}, {"Name": "Basic lifetime management for small objects with `proxy` (shared ownership) vs. `std::shared_ptr` (both with memory pool)", "TargetBenchmarkName": "BM_SmallObjectManagementWithProxy_SharedPooled", "BaselineBenchmarkName": "BM_SmallObjectManagementWithSharedPtr_Pooled"}, {"Name": "Basic lifetime management for small objects with `proxy` vs. `std::any`", "TargetBenchmarkName": "BM_SmallObjectManagementWithProxy", "BaselineBenchmarkName": "BM_SmallObjectManagementWithAny"}, {"Name": "Basic lifetime management for large objects with `proxy` (without memory pool) vs. `std::unique_ptr`", "TargetBenchmarkName": "BM_LargeObjectManagementWithProxy", "BaselineBenchmarkName": "BM_LargeObjectManagementWithUniquePtr"}, {"Name": "Basic lifetime management for large objects with `proxy` (with memory pool) vs. `std::unique_ptr`", "TargetBenchmarkName": "BM_LargeObjectManagementWithProxy_Pooled", "BaselineBenchmarkName": "BM_LargeObjectManagementWithUniquePtr"}, {"Name": "Basic lifetime management for large objects with `proxy` (exclusive ownership) vs. `std::shared_ptr` (both without memory pool)", "TargetBenchmarkName": "BM_LargeObjectManagementWithProxy", "BaselineBenchmarkName": "BM_LargeObjectManagementWithSharedPtr"}, {"Name": "Basic lifetime management for large objects with `proxy` (exclusive ownership) vs. `std::shared_ptr` (both with memory pool)", "TargetBenchmarkName": "BM_LargeObjectManagementWithProxy_Pooled", "BaselineBenchmarkName": "BM_LargeObjectManagementWithSharedPtr_Pooled"}, {"Name": "Basic lifetime management for large objects with `proxy` (shared ownership) vs. `std::shared_ptr` (both without memory pool)", "TargetBenchmarkName": "BM_LargeObjectManagementWithProxy_Shared", "BaselineBenchmarkName": "BM_LargeObjectManagementWithSharedPtr"}, {"Name": "Basic lifetime management for large objects with `proxy` (shared ownership) vs. `std::shared_ptr` (both with memory pool)", "TargetBenchmarkName": "BM_LargeObjectManagementWithProxy_SharedPooled", "BaselineBenchmarkName": "BM_LargeObjectManagementWithSharedPtr_Pooled"}, {"Name": "Basic lifetime management for large objects with `proxy` (without memory pool) vs. `std::any`", "TargetBenchmarkName": "BM_LargeObjectManagementWithProxy", "BaselineBenchmarkName": "BM_LargeObjectManagementWithAny"}, {"Name": "Basic lifetime management for large objects with `proxy` (with memory pool) vs. `std::any`", "TargetBenchmarkName": "BM_LargeObjectManagementWithProxy_Pooled", "BaselineBenchmarkName": "BM_LargeObjectManagementWithAny"}]}