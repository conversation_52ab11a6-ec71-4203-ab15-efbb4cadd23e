{"version": 1, "revision": 0, "rules": [{"primary-output": "untitled.dir\\Debug\\hello.obj", "outputs": ["C:\\Users\\<USER>\\CLionProjects\\untitled\\build\\untitled.dir\\Debug\\vc143.pdb", "untitled.dir\\Debug\\", "untitled.dir\\Debug\\hello.ifc"], "provides": [{"logical-name": "hello", "source-path": "c:\\users\\<USER>\\clionprojects\\untitled\\modules\\hello.ixx", "is-interface": true}], "requires": []}]}