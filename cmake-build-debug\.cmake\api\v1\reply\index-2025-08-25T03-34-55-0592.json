{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/bin/cmake.exe", "cpack": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/bin/cpack.exe", "ctest": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/bin/ctest.exe", "root": "C:/Users/<USER>/AppData/Local/Programs/CLion/bin/cmake/win/x64/share/cmake-4.0"}, "version": {"isDirty": false, "major": 4, "minor": 0, "patch": 2, "string": "4.0.2", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-ed31d6ad0c0af94dd5d2.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, {"jsonFile": "cache-v2-82faaf5217c3ebfaa670.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-3bd18e29cf1ca46e47aa.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-bfb918a9be99469f4394.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-82faaf5217c3ebfaa670.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-3bd18e29cf1ca46e47aa.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, "codemodel-v2": {"jsonFile": "codemodel-v2-ed31d6ad0c0af94dd5d2.json", "kind": "codemodel", "version": {"major": 2, "minor": 8}}, "toolchains-v1": {"jsonFile": "toolchains-v1-bfb918a9be99469f4394.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}}}