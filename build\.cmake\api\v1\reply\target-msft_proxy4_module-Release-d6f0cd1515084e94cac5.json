{"archive": {"commandFragments": [{"fragment": "/machine:X86", "role": "flags"}]}, "artifacts": [{"path": "Release/msft_proxy4_module.lib"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_library", "target_link_libraries", "target_compile_features", "target_sources"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 31, "parent": 0}, {"command": 1, "file": 0, "line": 47, "parent": 0}, {"command": 2, "file": 0, "line": 46, "parent": 0}, {"command": 3, "file": 0, "line": 40, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "/DWIN32 /D_WINDOWS /EHsc /O2 /Ob2 /DNDEBUG -std:c++latest -MD"}], "includes": [{"backtrace": 2, "path": "C:/Users/<USER>/CLionProjects/untitled/build/_deps/proxy-src/include"}], "language": "CXX", "languageStandard": {"backtraces": [3], "standard": "23"}, "sourceIndexes": [0]}], "dependencies": [{"id": "ZERO_CHECK::@6890427a1f51a3e7e1df"}], "fileSets": [{"baseDirectories": ["build/_deps/proxy-src/include"], "name": "CXX_MODULES", "type": "CXX_MODULES", "visibility": "PUBLIC"}], "id": "msft_proxy4_module::@6890427a1f51a3e7e1df", "name": "msft_proxy4_module", "nameOnDisk": "msft_proxy4_module.lib", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 4, "compileGroupIndex": 0, "path": "build/_deps/proxy-src/include/proxy/v4/proxy.ixx", "sourceGroupIndex": 0}], "type": "STATIC_LIBRARY"}