on:
  workflow_call:
    inputs:
      upload-artifacts:
        type: boolean
        default: false

jobs:
  bvt-mkdocs:
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Fetch all history for .GitInfo and .Lastmod

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.13"
          architecture: "x64"

      - name: Install dependencies
        run: |
          python3 -m pip install --upgrade pip
          python3 -m pip install -r mkdocs/requirements.txt

      - name: Build site
        run: mkdocs build --strict

      - name: Upload site artifact
        if: ${{ inputs.upload-artifacts }}
        uses: actions/upload-pages-artifact@v3
        with:
          path: ./site
