on:
  workflow_call:

jobs:
  bvt-report:
    runs-on: ubuntu-24.04
    steps:
    - uses: actions/checkout@v4

    - name: download all workflow run artifacts
      uses: actions/download-artifact@v4
      with:
        path: artifacts

    - name: build report generator
      run: |
        cd tools/report_generator
        cmake -B build -DCMAKE_BUILD_TYPE=Release
        cmake --build build -j

    - name: generate report
      run: |
        cat <<EOF > benchmarking-report.md
        # Benchmarking Report

        - Generated for: [Microsoft "Proxy" library](https://github.com/microsoft/proxy)
        - Commit ID: [${{ github.sha }}](https://github.com/microsoft/proxy/commit/${{ github.sha }})
        - Generated at: $(date -u +"%Y-%m-%dT%H:%M:%SZ")

        EOF
        tools/report_generator/build/report_generator tools/report_generator/report-config.json

    - name: archive benchmarking report
      uses: actions/upload-artifact@v4
      with:
        name: benchmarking-report
        path: benchmarking-report.md
