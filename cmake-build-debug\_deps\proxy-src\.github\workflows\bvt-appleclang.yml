on:
  workflow_call:

jobs:
  bvt-appleclang:
    runs-on: macos-15
    steps:
    - uses: actions/checkout@v4

    - name: check compiler versions
      run: |
        clang --version
        xcodebuild -version

    - name: build and run test with AppleClang
      run: |
        cmake -B build -GNinja -DCMAKE_CXX_STANDARD=23 -DCMAKE_BUILD_TYPE=Release -DPROXY_BUILD_MODULES=FALSE
        cmake --build build -j
        ctest --test-dir build -j
        mkdir build/drop
        chmod +x tools/dump_build_env.sh
        ./tools/dump_build_env.sh clang++ build/drop/env-info.json

    - name: run benchmarks
      run: |
        build/benchmarks/msft_proxy_benchmarks --benchmark_min_warmup_time=0.1 --benchmark_min_time=0.1s --benchmark_repetitions=30 --benchmark_enable_random_interleaving=true --benchmark_report_aggregates_only=true --benchmark_format=json > build/drop/benchmarking-results.json

    - name: archive benchmarking results
      uses: actions/upload-artifact@v4
      with:
        name: drop-appleclang
        path: build/drop/
