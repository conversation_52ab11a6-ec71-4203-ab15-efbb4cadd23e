on:
  workflow_call:

jobs:
  bvt-gcc:
    runs-on: ubuntu-24.04
    steps:
    - uses: actions/checkout@v4

    - name: install gcc
      run: |
        sudo apt install -y gcc-14 g++-14

    - name: check compiler version
      run: |
        g++-14 --version

    - name: build and run test with gcc 14
      run: |
        cmake -B build -GNinja -DCMAKE_C_COMPILER=gcc-14 -DCMAKE_CXX_COMPILER=g++-14 -DCMAKE_CXX_STANDARD=23 -DCMAKE_BUILD_TYPE=Release -DPROXY_BUILD_MODULES=TRUE
        cmake --build build -j
        ctest --test-dir build -j
        mkdir build/drop
        chmod +x tools/dump_build_env.sh
        ./tools/dump_build_env.sh g++-14 build/drop/env-info.json

    - name: run benchmarks
      run: |
        build/benchmarks/msft_proxy_benchmarks --benchmark_min_warmup_time=0.1 --benchmark_min_time=0.1s --benchmark_repetitions=30 --benchmark_enable_random_interleaving=true --benchmark_report_aggregates_only=true --benchmark_format=json > build/drop/benchmarking-results.json

    - name: archive benchmarking results
      uses: actions/upload-artifact@v4
      with:
        name: drop-gcc
        path: build/drop/
